package com.xiaomi.mone.codeflow.service.llm;

import static org.junit.jupiter.api.Assertions.*;

import java.io.IOException;
import java.io.InputStream;
import java.time.Duration;
import java.util.Properties;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import dev.langchain4j.data.message.AiMessage;
import dev.langchain4j.data.message.SystemMessage;
import dev.langchain4j.data.message.UserMessage;
import dev.langchain4j.model.chat.ChatLanguageModel;
import dev.langchain4j.model.openai.OpenAiChatModel;
import dev.langchain4j.model.output.Response;
import lombok.extern.slf4j.Slf4j;

/**
 * 用于测试langchain4j库集成
 * 独立测试，不依赖Spring上下文
 */
@Slf4j
class LangchainTest {

    private String apiKey;
    private String baseUrl;
    private String modelName;

    @BeforeEach
    void setUp() throws IOException {
        // 从配置文件加载测试配置
        Properties props = new Properties();
        try (InputStream is = getClass().getClassLoader().getResourceAsStream("application-test.properties")) {
            if (is != null) {
                props.load(is);
                apiKey = props.getProperty("llm.api-key");
                baseUrl = props.getProperty("llm.base-url");
                modelName = props.getProperty("llm.api-model");
            } else {
                // 如果配置文件不存在，使用默认值
                log.warn("application-test.properties not found, using default values");
                apiKey = "test-api-key";
                baseUrl = "https://api.openai.com/v1";
                modelName = "gpt-3.5-turbo";
            }
        }
    }

    @Test
    void testLangchain4j() {
        log.info("测试langchain4j库集成");
        log.info("使用配置: baseUrl={}, modelName={}", baseUrl, modelName);

        // 创建一个简单的模型实例用于测试
        ChatLanguageModel model = OpenAiChatModel.builder()
                .baseUrl(baseUrl)
                .apiKey(apiKey)
                .modelName(modelName)
                .temperature(0.7)
                .maxTokens(100)
                .timeout(Duration.ofSeconds(30))
                .build();

        // 验证模型实例创建成功
        assertNotNull(model, "ChatLanguageModel实例应该创建成功");
        log.info("ChatLanguageModel实例创建成功");

        try {
            // 创建测试消息
            SystemMessage systemMessage = SystemMessage.from("你是一个测试助手");
            UserMessage userMessage = UserMessage.from("测试一下");

            // 发送测试请求
            Response<AiMessage> response = model.generate(systemMessage, userMessage);

            // 检查响应
            if (response != null && response.content() != null) {
                log.info("langchain4j测试响应: {}", response.content().text());
                assertNotNull(response.content().text(), "响应内容不应为空");
            } else {
                log.warn("langchain4j测试响应为空，可能是网络或API配置问题");
                // 在测试环境中，我们只验证库的集成是否正常，不强制要求API调用成功
            }
        } catch (Exception e) {
            log.warn("langchain4j API调用失败，但库集成正常: {}", e.getMessage());
            // 在测试环境中，API调用失败是可以接受的，只要库能正常加载和初始化
        }

        log.info("langchain4j库集成测试完成");
    }
}