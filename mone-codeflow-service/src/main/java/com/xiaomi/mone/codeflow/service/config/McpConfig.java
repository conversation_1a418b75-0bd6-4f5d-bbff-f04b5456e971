package com.xiaomi.mone.codeflow.service.config;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xiaomi.mone.codeflow.nacos.NacosJsonValue;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * MCP配置类
 * 支持通用Nacos JSON配置读取，动态拉取和解析mcp.json
 */
@Slf4j
@Component
@Data
public class McpConfig {

    @NacosJsonValue(dataId = "mcp.json", autoRefreshed = false)
    private String mcpJson;

    private final ObjectMapper objectMapper = new ObjectMapper();
    private Map<String, McpServerConfig> mcpServers = new HashMap<>();

    @PostConstruct
    public void init() {
        parseMcpConfig(mcpJson);
    }

    /**
     * 解析MCP配置
     */
    private void parseMcpConfig(String mcpConfigJson) {
        try {
            if (mcpConfigJson != null && !mcpConfigJson.trim().isEmpty() && !mcpConfigJson.equals("{}")) {
                Map<String, Object> configMap = objectMapper.readValue(mcpConfigJson, new TypeReference<Map<String, Object>>() {});
                if (configMap.containsKey("mcpServers")) {
                    @SuppressWarnings("unchecked")
                    Map<String, Map<String, Object>> serversMap = (Map<String, Map<String, Object>>) configMap.get("mcpServers");
                    mcpServers.clear();
                    for (Map.Entry<String, Map<String, Object>> entry : serversMap.entrySet()) {
                        String serverName = entry.getKey();
                        Map<String, Object> serverConfig = entry.getValue();
                        McpServerConfig config = new McpServerConfig();
                        config.setName(serverName);
                        config.setCommand((String) serverConfig.get("command"));
                        @SuppressWarnings("unchecked")
                        List<String> args = (List<String>) serverConfig.get("args");
                        config.setArgs(args);
                        mcpServers.put(serverName, config);
                        log.info("加载MCP服务器配置: {} -> {}", serverName, config);
                    }
                } else {
                    log.warn("MCP配置中未找到mcpServers字段");
                }
            } else {
                log.warn("MCP配置为空或无效，使用默认配置");
                mcpServers.clear();
            }
        } catch (Exception e) {
            log.error("解析MCP配置失败", e);
            mcpServers.clear();
        }
    }

    /**
     * 获取MCP服务器配置
     */
    public Map<String, McpServerConfig> getMcpServers() {
        parseMcpConfig(mcpJson);
        return mcpServers;
    }

    /**
     * MCP服务器配置
     */
    @Data
    public static class McpServerConfig {
        private String name;
        private String command;
        private List<String> args;

        @Override
        public String toString() {
            return String.format("McpServerConfig{name='%s', command='%s', args=%s}", name, command, args);
        }
    }

    /**
     * 检查MCP是否启用且有有效配置
     */
    public boolean isValidMcpConfig() {
        return !getMcpServers().isEmpty();
    }

    /**
     * 获取配置信息用于状态检查
     */
    public Map<String, Object> getConfigInfo() {
        Map<String, Object> info = new HashMap<>();
        info.put("serversCount", getMcpServers().size());
        info.put("servers", getMcpServers().keySet());
        return info;
    }
}
