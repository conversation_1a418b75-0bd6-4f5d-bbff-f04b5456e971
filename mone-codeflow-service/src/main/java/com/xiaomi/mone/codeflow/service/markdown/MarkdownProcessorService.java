package com.xiaomi.mone.codeflow.service.markdown;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import java.io.IOException;
import java.util.List;

/**
 * 用于处理包含Mermaid图的Markdown文本的服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MarkdownProcessorService {

    private final MermaidExtractorService extractorService;
    private final MermaidImageService mermaidImageService;

    /**
     * 处理Markdown文本，提取Mermaid图，将其转换为图片，并在原代码块后插入图片。
     *
     * @param markdownContent 输入的Markdown文本内容
     * @return 插入图片后的处理过的Markdown文本内容
     */
    public String processMarkdownText(String markdownContent) {
        log.info("Processing Markdown content");

        // 提取 Mermaid 代码块
        List<MermaidExtractorService.MermaidBlock> mermaidBlocks =
                extractorService.extractMermaidBlocks(markdownContent);

        if (mermaidBlocks.isEmpty()) {
            log.info("No Mermaid blocks found in the content");
            return markdownContent;
        }

        // 逆序处理代码块以保持正确的索引
        String processedContent = insertMermaidImages(markdownContent, mermaidBlocks);

        log.info("Successfully processed Markdown content");
        return processedContent;
    }

    /**
     * 处理Markdown文件，将Mermaid图转为图片并上传，插入图片URL。
     * @param markdownContent 输入Markdown内容
     * @return 处理后的Markdown内容
     * @throws IOException IO异常
     */
    public String processMarkdownTextAndUploadImages(String markdownContent) {
        log.info("Processing Markdown content (upload image mode)");
        List<MermaidExtractorService.MermaidBlock> mermaidBlocks = extractorService.extractMermaidBlocks(markdownContent);
        if (mermaidBlocks.isEmpty()) {
            log.info("No Mermaid blocks found, return original content");
            return markdownContent;
        }
        StringBuilder newMarkdown = new StringBuilder();
        int lastIndex = 0;
        for (MermaidExtractorService.MermaidBlock block : mermaidBlocks) {
            newMarkdown.append(markdownContent, lastIndex, block.getEndIndex());
            lastIndex = block.getEndIndex();
            try {
                // 将 Mermaid 代码转换为图片并上传
                String imageUrl = mermaidImageService.convertMermaidToImageUrl(block.getCode());

                if (imageUrl != null && !imageUrl.isEmpty()) {
                    // 使用上传的图片URL插入图片 markdown
                    newMarkdown.append("\n\n![Mermaid Diagram](").append(imageUrl).append(")\n\n");
                    log.debug("Inserted image for Mermaid block at position {}", block.getEndIndex());
                } else {
                    log.error("Failed to process and upload image for Mermaid block at position {}", block.getStartIndex());
                }

            } catch (Exception e) {
                log.error("Failed to process Mermaid block at position {}: {}",
                        block.getStartIndex(), e.getMessage());
            }
        }
        newMarkdown.append(markdownContent.substring(lastIndex));
        return newMarkdown.toString();
    }

    /**
     * 在每个Mermaid代码块后插入Mermaid图片
     *
     * @param content 原始Markdown内容
     * @param mermaidBlocks Mermaid代码块列表
     * @return 插入图片后的处理内容
     */
    private String insertMermaidImages(String content, List<MermaidExtractorService.MermaidBlock> mermaidBlocks) {
        StringBuilder result = new StringBuilder(content);

        // 逆序处理代码块以保持正确的索引
        for (int i = mermaidBlocks.size() - 1; i >= 0; i--) {
            MermaidExtractorService.MermaidBlock block = mermaidBlocks.get(i);

            try {
                // 调用服务将Mermaid代码转为图片并上传，获取图片URL
                String imageUrl = mermaidImageService.convertMermaidToImageUrl(block.getCode());

                if (imageUrl != null && !imageUrl.isEmpty()) {
                    // 使用上传的图片URL创建markdown图片语法
                    String imageMarkdown = String.format("%n%n![Mermaid Diagram](%s)%n", imageUrl);

                    // 插入到Mermaid代码块之后
                    result.insert(block.getEndIndex(), imageMarkdown);

                    log.debug("Inserted Mermaid image at position {}", block.getEndIndex());
                } else {
                    log.error("Failed to process Mermaid block at position {}: Unable to get image URL",
                            block.getStartIndex());
                    // 如果无法获取图片URL，插入失败注释
                     result.insert(block.getEndIndex(), "\n\n<!-- Mermaid image conversion or upload failed -->\n\n");
                }

            } catch (Exception e) {
                log.error("Failed to process Mermaid block at position {}: {}",
                        block.getStartIndex(), e.getMessage());
                // 即使一个代码块处理失败，也继续处理其他代码块
            }
        }

        return result.toString();
    }
}
