package com.xiaomi.mone.codeflow.service.feishu;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class FeiShuStorageRedisService {

    private static final String REDIS_KEY_PREFIX = "mone-codeflow:";
    private static final String ACCESS_TOKEN_SUFFIX = ":feishu_access_token";
    private static final String REFRESH_TOKEN_SUFFIX = ":feishu_refresh_token";

    private final StringRedisTemplate stringRedisTemplate;

    @Autowired
    public FeiShuStorageRedisService(StringRedisTemplate stringRedisTemplate) {
        this.stringRedisTemplate = stringRedisTemplate;
    }

    @PostConstruct
    public void testConnection() {
        try {
            String ping = stringRedisTemplate.getConnectionFactory().getConnection().ping();
            log.info("Redis connection successful, ping response: {}", ping);
        } catch (Exception e) {
            log.error("Redis connection failed during @PostConstruct", e);
        }
    }

    public String getAccessToken(String account) {
        String key = REDIS_KEY_PREFIX + account + ACCESS_TOKEN_SUFFIX;
        return stringRedisTemplate.opsForValue().get(key);
    }

    public String getRefreshToken(String account) {
        String key = REDIS_KEY_PREFIX + account + REFRESH_TOKEN_SUFFIX;
        return stringRedisTemplate.opsForValue().get(key);
    }

    public void saveAccessToken(String account, String accessToken, long expiresIn) {
        String key = REDIS_KEY_PREFIX + account + ACCESS_TOKEN_SUFFIX;
        stringRedisTemplate.opsForValue().set(key, accessToken, expiresIn - 60, TimeUnit.SECONDS);
    }

    public void saveRefreshToken(String account, String refreshToken, long refreshExpiresIn) {
        String key = REDIS_KEY_PREFIX + account + REFRESH_TOKEN_SUFFIX;
        stringRedisTemplate.opsForValue().set(key, refreshToken, refreshExpiresIn - 60, TimeUnit.SECONDS);
    }
}