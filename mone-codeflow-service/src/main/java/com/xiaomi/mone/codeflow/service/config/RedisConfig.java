package com.xiaomi.mone.codeflow.service.config;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.jedis.JedisClientConfiguration;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import org.springframework.data.redis.core.StringRedisTemplate;

@Configuration
public class RedisConfig {

    @NacosValue("${redis.url}")
    private String redisUrl;

    @NacosValue("${redis.password}")
    private String redisPassword;

    @Bean
    public StringRedisTemplate stringRedisTemplate() {
        String[] parts = redisUrl.split(":");
        String host = parts[0];
        int port = Integer.parseInt(parts[1]);

        RedisStandaloneConfiguration redisConfig = new RedisStandaloneConfiguration();
        redisConfig.setHostName(host);
        redisConfig.setPort(port);
        redisConfig.setPassword(redisPassword);

        JedisClientConfiguration clientConfig = JedisClientConfiguration.builder().build();
        JedisConnectionFactory jedisConnectionFactory = new JedisConnectionFactory(redisConfig, clientConfig);
        jedisConnectionFactory.afterPropertiesSet();

        return new StringRedisTemplate(jedisConnectionFactory);
    }
}