package com.xiaomi.mone.codeflow.nacos;

import com.alibaba.nacos.api.config.ConfigService;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.stereotype.Component;
import java.lang.reflect.Field;

@Component
public class NacosJsonValueProcessor implements BeanPostProcessor {

    @Autowired
    private ConfigService configService;

    @Override
    public Object postProcessBeforeInitialization(Object bean, String beanName) throws BeansException {
        Field[] fields = bean.getClass().getDeclaredFields();
        for (Field field : fields) {
            NacosJsonValue annotation = field.getAnnotation(NacosJsonValue.class);
            if (annotation != null) {
                String dataId = annotation.dataId();
                String group = annotation.group();
                try {
                    String value = configService.getConfig(dataId, group, 3000);
                    field.setAccessible(true);
                    field.set(bean, value);
                } catch (Exception e) {
                    throw new RuntimeException("Failed to inject Nacos config for dataId: " + dataId, e);
                }
            }
        }
        return bean;
    }
} 