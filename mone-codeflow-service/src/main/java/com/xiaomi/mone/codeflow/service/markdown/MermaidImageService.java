package com.xiaomi.mone.codeflow.service.markdown;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.xiaomi.mone.codeflow.service.fds.UploadFileService;

import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.BufferedReader;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Comparator;
import java.util.stream.Collectors;

@Service
@Slf4j
public class MermaidImageService {

    @NacosValue(value = "${mmdc.installation.path.windows}", autoRefreshed = true)
    private String mmdcPathWindows;

    @NacosValue(value = "${mmdc.installation.path.linux}", autoRefreshed = true)
    private String mmdcPathLinux;

    @NacosValue(value = "${mmdc.installation.path.mac}", autoRefreshed = true)
    private String mmdcPathMac;

    private final UploadFileService uploadFileService;

    @Autowired
    public MermaidImageService(UploadFileService uploadFileService) {
        this.uploadFileService = uploadFileService;
    }

    /**
     * Converts Mermaid syntax to an image and uploads it.
     *
     * @param mermaidSyntax The Mermaid syntax string.
     * @return The URL of the uploaded image, or null if conversion or upload fails.
     */
    public String convertMermaidToImageUrl(String mermaidSyntax) {
        Path tempDir = null;
        try {
            // Create a unique temporary directory for this conversion
            tempDir = Files.createTempDirectory("mermaid_conversion_");
            Path inputFilePath = tempDir.resolve("input.mmd");
            Path outputFilePath = tempDir.resolve("output.png");

            // Write Mermaid syntax to input file
            Files.write(inputFilePath, mermaidSyntax.getBytes());

            // Determine OS and get mmdc path
            String os = System.getProperty("os.name").toLowerCase();
            String mmdcPath;
            if (os.contains("win")) {
                mmdcPath = mmdcPathWindows;
            } else if (os.contains("mac")) {
                mmdcPath = mmdcPathMac;
            } else {
                mmdcPath = mmdcPathLinux;
            }

            // Build and execute the mmdc command
            ProcessBuilder processBuilder = new ProcessBuilder(mmdcPath, "-i", inputFilePath.toString(), "-o", outputFilePath.toString());
            processBuilder.redirectErrorStream(true);
            Process process = processBuilder.start();

            // Read process output (for logging errors)
            String processOutput = new BufferedReader(new InputStreamReader(process.getInputStream())).lines().collect(Collectors.joining("\n"));
            int exitCode = process.waitFor();

            if (exitCode != 0) {
                log.error("mmdc conversion failed with exit code {}. Output:\n{}", exitCode, processOutput);
                return null;
            }

            // Check if the output file was created
            if (!Files.exists(outputFilePath)) {
                log.error("mmdc output file not found: {}", outputFilePath);
                return null;
            }

            // Upload the generated image
            File outputFile = outputFilePath.toFile();
            String key = "mermaid/" + System.currentTimeMillis() + "_" + outputFile.getName(); // Example key
            String imageUrl = uploadFileService.uploadFile(key, outputFile, false); // Assuming upload to outer bucket

            if (imageUrl.isEmpty()) {
                log.error("Failed to upload generated image to FDS.");
                return null;
            }

            log.info("Mermaid image converted and uploaded successfully: {}", imageUrl);
            return imageUrl;

        } catch (Exception e) {
            log.error("Error during Mermaid conversion or upload", e);
            return null;
        } finally {
            // Clean up temporary directory and its contents
            if (tempDir != null && Files.exists(tempDir)) {
                try {
                    Files.walk(tempDir)
                         .sorted(Comparator.reverseOrder())
                         .map(Path::toFile)
                         .forEach(File::delete);
                    log.debug("Cleaned up temporary directory: {}", tempDir);
                } catch (Exception e) {
                    log.error("Failed to clean up temporary directory: {}", tempDir, e);
                }
            }
        }
    }
} 