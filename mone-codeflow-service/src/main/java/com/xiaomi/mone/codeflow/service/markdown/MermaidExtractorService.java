package com.xiaomi.mone.codeflow.service.markdown;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Service for extracting Mermaid code blocks from Markdown content
 */
@Slf4j
@Service
public class MermaidExtractorService {

    private static final Pattern MERMAID_PATTERN = Pattern.compile(
            "```mermaid\\s*\\n([\\s\\S]*?)\\n```", 
            Pattern.CASE_INSENSITIVE | Pattern.MULTILINE
    );

    /**
     * Extract all Mermaid code blocks from markdown content
     * 
     * @param markdownContent the markdown content to extract from
     * @return list of MermaidBlock objects containing code and position info
     */
    public List<MermaidBlock> extractMermaidBlocks(String markdownContent) {
        log.info("Starting Mermaid code extraction");
        
        List<MermaidBlock> mermaidBlocks = new ArrayList<>();
        Matcher matcher = MERMAID_PATTERN.matcher(markdownContent);
        
        while (matcher.find()) {
            String mermaidCode = matcher.group(1).trim();
            int startIndex = matcher.start();
            int endIndex = matcher.end();
            
            MermaidBlock block = new MermaidBlock(mermaidCode, startIndex, endIndex);
            mermaidBlocks.add(block);
            
            log.debug("Found Mermaid block at position {}-{}: {}", 
                     startIndex, endIndex, mermaidCode.substring(0, Math.min(50, mermaidCode.length())));
        }
        
        log.info("Extracted {} Mermaid blocks", mermaidBlocks.size());
        return mermaidBlocks;
    }

    /**
     * Data class to hold Mermaid block information
     */
    public static class MermaidBlock {
        private final String code;
        private final int startIndex;
        private final int endIndex;

        public MermaidBlock(String code, int startIndex, int endIndex) {
            this.code = code;
            this.startIndex = startIndex;
            this.endIndex = endIndex;
        }

        public String getCode() {
            return code;
        }

        public int getStartIndex() {
            return startIndex;
        }

        public int getEndIndex() {
            return endIndex;
        }
    }
}
