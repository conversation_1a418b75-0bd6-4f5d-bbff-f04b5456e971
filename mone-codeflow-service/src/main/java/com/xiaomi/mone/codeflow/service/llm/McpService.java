package com.xiaomi.mone.codeflow.service.llm;

import com.xiaomi.mone.codeflow.service.config.AppConfig;
import com.xiaomi.mone.codeflow.service.config.McpConfig;
import com.xiaomi.mone.codeflow.service.config.McpConfig.McpServerConfig;
import dev.langchain4j.data.message.AiMessage;
import dev.langchain4j.data.message.SystemMessage;
import dev.langchain4j.data.message.UserMessage;
import dev.langchain4j.model.chat.ChatLanguageModel;
import dev.langchain4j.model.output.Response;
import dev.langchain4j.mcp.client.DefaultMcpClient;
import dev.langchain4j.mcp.client.McpClient;
import dev.langchain4j.mcp.McpToolProvider;
import dev.langchain4j.service.tool.ToolProvider;
import dev.langchain4j.mcp.client.transport.stdio.StdioMcpTransport;
import dev.langchain4j.service.AiServices;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PreDestroy;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * MCP (Model Context Protocol) 服务
 * 动态管理多MCP客户端，聚合上下文，调用大模型
 */
@Slf4j
@Service
public class McpService {

    private final ChatLanguageModel chatModel;
    private final AppConfig appConfig;
    private final McpConfig mcpConfig;
    // 缓存MCP客户端，避免重复创建
    private final Map<String, McpClient> mcpClientCache = new ConcurrentHashMap<>();

    @Autowired
    public McpService(ChatLanguageModel chatModel, AppConfig appConfig, McpConfig mcpConfig) {
        this.chatModel = chatModel;
        this.appConfig = appConfig;
        this.mcpConfig = mcpConfig;
    }

    /**
     * 处理MCP问题查询
     * @param question 用户问题
     * @return 大模型响应
     */
    public String processQuestion(String question) {
        if (!mcpConfig.isValidMcpConfig()) {
            log.warn("MCP功能已禁用或配置无效");
            return "MCP功能当前已禁用或配置无效，请联系管理员检查配置。";
        }
        try {
            // 1. 动态获取所有MCP客户端
            List<McpClient> mcpClients = getAllMcpClients();
            if (mcpClients.isEmpty()) {
                return "无可用MCP服务器，请检查配置。";
            }
            // 2. 构建MCP工具链
            ToolProvider toolProvider = McpToolProvider.builder()
                    .mcpClients(mcpClients)
                    .build();
            // 3. 构建Bot
            Bot bot = AiServices.builder(Bot.class)
                    .chatLanguageModel(chatModel)
                    .toolProvider(toolProvider)
                    .build();
            // 4. 调用大模型
            String response = bot.chat(question);
            return response;
        } catch (Exception e) {
            log.error("处理MCP问题时发生错误", e);
            return "处理问题时发生错误: " + e.getMessage();
        }
    }

    /**
     * 获取所有MCP客户端实例
     */
    private List<McpClient> getAllMcpClients() {
        Map<String, McpServerConfig> servers = mcpConfig.getMcpServers();
        List<McpClient> clients = new ArrayList<>();
        for (Map.Entry<String, McpServerConfig> entry : servers.entrySet()) {
            String serverName = entry.getKey();
            McpServerConfig config = entry.getValue();
            try {
                McpClient client = mcpClientCache.computeIfAbsent(serverName, k -> createMcpClient(config));
                if (client != null) {
                    clients.add(client);
                }
            } catch (Exception e) {
                log.error("创建MCP客户端失败: {}", serverName, e);
            }
        }
        return clients;
    }

    /**
     * 创建单个MCP客户端
     */
    private McpClient createMcpClient(McpServerConfig config) {
        String serverName = config.getName();
        try {
            log.info("[MCP] 准备创建MCP客户端: serverName={}, command={}, args={}", serverName, config.getCommand(), config.getArgs());
            List<String> command = new ArrayList<>();
            if (config.getCommand() != null) {
                command.add(config.getCommand());
            }
            if (config.getArgs() != null) {
                command.addAll(config.getArgs());
            }
            log.info("[MCP] 构建StdioMcpTransport, serverName={}, commandList={}", serverName, command);
            StdioMcpTransport transport = new StdioMcpTransport.Builder()
                    .command(command)
                    .logEvents(true)
                    .build();
            log.info("[MCP] StdioMcpTransport 构建完成, serverName={}", serverName);
            DefaultMcpClient client = new DefaultMcpClient.Builder()
                    .transport(transport)
                    .build();
            log.info("[MCP] DefaultMcpClient 构建完成, serverName={}", serverName);
            return client;
        } catch (Exception e) {
            log.error("[MCP] 创建MCP客户端异常: {}", config.getName(), e);
            return null;
        }
    }

    /**
     * 检查MCP服务状态
     */
    public boolean checkMcpStatus() {
        if (!mcpConfig.isValidMcpConfig()) {
            return false;
        }
        Map<String, McpServerConfig> servers = mcpConfig.getMcpServers();
        for (McpServerConfig config : servers.values()) {
            if (config.getCommand() != null && !config.getCommand().isEmpty()) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取MCP配置信息
     */
    public Map<String, Object> getMcpConfig() {
        Map<String, Object> config = mcpConfig.getConfigInfo();
        config.put("status", checkMcpStatus());
        return config;
    }

    /**
     * 关闭所有MCP客户端资源
     */
    @PreDestroy
    public void closeAllClients() {
        for (McpClient client : mcpClientCache.values()) {
            try {
                client.close();
            } catch (Exception e) {
                log.warn("关闭MCP客户端失败", e);
            }
        }
        mcpClientCache.clear();
    }

    /**
     * Bot接口定义
     */
    public interface Bot {
        String chat(String question);
    }
}
