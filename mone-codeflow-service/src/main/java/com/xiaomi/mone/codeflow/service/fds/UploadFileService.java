package com.xiaomi.mone.codeflow.service.fds;

import com.xiaomi.infra.galaxy.fds.client.FDSClientConfiguration;
import com.xiaomi.infra.galaxy.fds.client.GalaxyFDS;
import com.xiaomi.infra.galaxy.fds.client.GalaxyFDSClient;
import com.xiaomi.infra.galaxy.fds.client.credential.BasicFDSCredential;
import com.xiaomi.infra.galaxy.fds.client.credential.GalaxyFDSCredential;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.alibaba.nacos.api.config.annotation.NacosValue;

import javax.annotation.PostConstruct;
import java.io.File;
import java.net.URI;
import java.util.Date;
import java.time.LocalDate;
import java.time.ZoneId;

@Service
@Slf4j
public class UploadFileService {

    private static final Date expireData = Date.from(LocalDate.of(2099, 1, 1).atStartOfDay(ZoneId.systemDefault()).toInstant());

    @NacosValue(value = "${fds.access.key}", autoRefreshed = true)
    private String accessKey;

    @NacosValue(value = "${fds.access.ppwwdd}", autoRefreshed = true)
    private String accessSecret;

    @NacosValue(value = "${fds.access.endpoint}", autoRefreshed = true)
    private String endpoint;

    @NacosValue(value = "${fds.bucket.name}", autoRefreshed = true)
    private String bucketName;

    @NacosValue(value = "${fds.bucket.inner.name}", autoRefreshed = true)
    private String bucketInnerName;

    private GalaxyFDS fdsClient;

    @PostConstruct
    private void init() {
        GalaxyFDSCredential credential = new BasicFDSCredential(accessKey, accessSecret);
        FDSClientConfiguration fdsConfig = new FDSClientConfiguration(endpoint);
        log.info("FDSClientConfiguration initialized for endpoint: {}", endpoint);
        fdsConfig.enableHttps(true);
        fdsConfig.enableCdnForUpload(false);
        fdsConfig.enableCdnForDownload(false);

        fdsClient = new GalaxyFDSClient(credential, fdsConfig);
    }

    /**
     * 将文件上传到 FDS。
     *
     * @param key FDS 中的对象键（文件路径）。
     * @param file 要上传的文件。
     * @param inInner 是否上传到内部桶。
     * @return 上传文件的预签名 URI，如果上传失败则返回空字符串。
     */
    public String uploadFile(String key, File file, boolean inInner) {
        log.info("UploadFileService#uploadFile key={}", key);
        try {
            String targetBucket = inInner ? bucketInnerName : bucketName;
            fdsClient.putObject(targetBucket, key, file);
            URI uri = fdsClient.generatePresignedUri(targetBucket, key, expireData);
            log.info("File uploaded successfully to {} with key {}. Presigned URI: {}", targetBucket, key, uri.toString());
            return uri.toString();
        } catch (Exception e) {
            log.error("Failed to upload file to FDS, msg: {}", e.getMessage(), e);
            return "";
        }
    }

} 