package com.xiaomi.mone.codeflow.server.api;

import java.io.IOException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.Map;
import java.util.TreeMap;
import java.io.File;
import java.util.List;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import com.xiaomi.mone.codeflow.dto.user.UserInfoVo;
import com.xiaomi.mone.codeflow.service.cas.UserService;
import com.xiaomi.mone.codeflow.service.document.DocumentStorageService;
import com.xiaomi.mone.codeflow.service.feishu.FeiShuDocumentService;
import com.xiaomi.mone.codeflow.service.llm.RDGeneratorStreamService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import com.xiaomi.mone.codeflow.service.feishu.FeiShuUploadFileService;
import com.xiaomi.mone.codeflow.service.feishu.IProjectWorkItemService;
import com.xiaomi.mone.codeflow.service.llm.RDGeneratorService;
import com.xiaomi.mone.codeflow.common.enums.FileTypeEnum;
import com.xiaomi.mone.codeflow.service.feishu.entitys.DocumentEntity;
import com.xiaomi.mone.codeflow.service.feishu.results.DocumentResult;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.xiaomi.mone.codeflow.service.llm.KnowledgeExtractorService;
import com.xiaomi.mone.codeflow.server.provider.KnowledgeService;
import com.xiaomi.mone.codeflow.server.model.KnowledgeQueryRequest;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xiaomi.mone.codeflow.service.feishu.FeiShuAuthParamsService;
import com.xiaomi.mone.codeflow.service.feishu.entitys.DocumentInfoEntity;
import com.xiaomi.mone.codeflow.service.feishu.results.DocumentInfoResult;
import com.xiaomi.mone.codeflow.service.feishu.FeiShuSpaceService;
import com.xiaomi.mone.codeflow.service.markdown.MarkdownProcessorService;
import com.xiaomi.mone.codeflow.service.template.RDTemplateManager;
import com.xiaomi.mone.codeflow.service.template.RDTemplateManager.SectionConfig;

@Slf4j
@RestController
@RequestMapping("/api/rd-generator")
public class RDGeneratorStreamController {
    
    private final RDGeneratorStreamService rdGeneratorStreamService;
    private final FeiShuDocumentService feiShuDocumentService;
    private final DocumentStorageService documentStorageService;
    private final UserService userService;
    private final RDGeneratorService rdGeneratorService;
    private final FeiShuUploadFileService feiShuUploadFileService;
    private final IProjectWorkItemService projectWorkItemService;
    private final ExecutorService executorService;
    private final KnowledgeService knowledgeService;
    private final KnowledgeExtractorService knowledgeExtractorService;
    private final FeiShuAuthParamsService feiShuAuthParamsService;
    private final FeiShuSpaceService feiShuSpaceService;
    private final MarkdownProcessorService markdownProcessorService;
    
    @NacosValue(value = "${m78.knowledge.api.userName:defaultUser}", autoRefreshed = true)
    private String defaultUserName;
    @NacosValue(value = "${m78.knowledge.api.knowledgeId:1}", autoRefreshed = true)
    private Integer defaultKnowledgeId;
    @NacosValue(value = "${m78.knowledge.api.limit:3}", autoRefreshed = true)
    private Integer defaultLimit;
    
    @Autowired
    public RDGeneratorStreamController(RDGeneratorStreamService rdGeneratorStreamService, 
                                      FeiShuDocumentService feiShuDocumentService,
                                      DocumentStorageService documentStorageService,
                                      UserService userService,
                                      RDGeneratorService rdGeneratorService,
                                      FeiShuUploadFileService feiShuUploadFileService,
                                      IProjectWorkItemService projectWorkItemService,
                                      KnowledgeExtractorService knowledgeExtractorService,
                                      KnowledgeService knowledgeService,
                                      FeiShuAuthParamsService feiShuAuthParamsService,
                                      FeiShuSpaceService feiShuSpaceService,
                                      MarkdownProcessorService markdownProcessorService) {
        this.rdGeneratorStreamService = rdGeneratorStreamService;
        this.feiShuDocumentService = feiShuDocumentService;
        this.documentStorageService = documentStorageService;
        this.userService = userService;
        this.rdGeneratorService = rdGeneratorService;
        this.feiShuUploadFileService = feiShuUploadFileService;
        this.projectWorkItemService = projectWorkItemService;
        this.knowledgeExtractorService = knowledgeExtractorService;
        this.knowledgeService = knowledgeService;
        this.executorService = Executors.newCachedThreadPool();
        this.feiShuAuthParamsService = feiShuAuthParamsService;
        this.feiShuSpaceService = feiShuSpaceService;
        this.markdownProcessorService = markdownProcessorService;
    }
    
    @GetMapping(value = "/stream")
    public String methodNotAllowed() {
        return "该接口仅支持POST请求方式，请使用POST方法访问";
    }
    
    @Data
    public static class GenerateRequest {
        private String feishuUrl;
        private String prdContent;
        private String apiDocContent;
        private String sqlDocContent;
        private String knowledgeSpaceId;
        private Integer knowledgeId;
        private String meegoUrl;
    }
    
    /**
     * 解析文档URL获取文档ID
     *
     * @param docUrl 飞书文档URL
     * @return 文档ID，如果URL为空或格式不正确则返回null
     */
    private String parseDocumentId(String docUrl) {
        if (StringUtils.isEmpty(docUrl)) {
            return null;
        }

        int lastSlashIndex = docUrl.lastIndexOf("/");
        if (lastSlashIndex == -1 || lastSlashIndex == docUrl.length() - 1) {
            return null;
        }

        return docUrl.substring(lastSlashIndex + 1);
    }
    
    /**
     * 向SSE客户端发送错误信息
     * 使用单独的线程发送错误，确保浏览器有足够时间建立连接
     *
     * @param emitter SSE发射器
     * @param errorMessage 错误信息
     */
    private void sendErrorToClient(SseEmitter emitter, String errorMessage) {
        // 使用单独的线程发送错误，确保浏览器有足够时间建立连接
        executorService.execute(() -> {
            try {
                // 短暂延迟确保浏览器已建立连接
                Thread.sleep(500);
                
                // 先检查emitter是否已经完成
                if (emitter != null) {
                    // 发送错误事件前先发送一个心跳确保连接已建立
                    try {
                        emitter.send(SseEmitter.event()
                            .name("heartbeat")
                            .data(""));
                        
                        // 短暂延迟确保客户端已准备好接收事件
                        Thread.sleep(100);
                        
                        // 发送实际错误事件
                        emitter.send(SseEmitter.event()
                            .name("error")
                            .id(String.valueOf(System.currentTimeMillis()))  // 添加唯一ID
                            .data(errorMessage, MediaType.TEXT_PLAIN));
                        
                        // 再次短暂延迟确保错误消息被发送
                        Thread.sleep(100);
                        
                        // 最后完成事件流
                        emitter.complete();
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        log.warn("发送错误消息被中断", ie);
                    } catch (IOException ioEx) {
                        log.error("发送错误消息失败", ioEx);
                        try {
                            emitter.completeWithError(ioEx);
                        } catch (Exception e) {
                            log.error("结束事件流失败", e);
                        }
                    }
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("发送错误消息被中断", e);
            } catch (Exception e) {
                // 捕获所有其他异常
                log.error("发送错误消息时发生未预期异常", e);
                try {
                    if (emitter != null) {
                        emitter.completeWithError(e);
                    } else {
                        log.error("无法发送错误消息，emitter为空");
                    }
                } catch (Exception ex) {
                    log.error("结束事件流失败", ex);
                }
            }
        });
    }
    
    /**
     * 获取当前登录用户account
     * 
     * @return 用户account，未登录时返回null
     */
    private String getCurrentUserAccount() {
        UserInfoVo userInfo = userService.getCurrentUserInfo();
        if (userInfo != null && StringUtils.isNotBlank(userInfo.getAccount())) {
            return userInfo.getAccount();
        }
        return null;
    }
    
    /**
     * 过滤掉文本中的章节标记（<chapter:数字>和</chapter:数字>）
     * 
     * @param text 需要过滤的文本
     * @return 过滤后的文本
     */
    private String filterChapterMarkers(String text) {
        if (StringUtils.isEmpty(text)) {
            return text;
        }
        // 过滤掉<chapter:数字>和</chapter:数字>格式的标记
        return text.replaceAll("<chapter:\\d+>|</chapter:\\d+>", "");
    }
    
    /**
     * 流式生成RD文档
     * @param request 生成请求，包含PRD内容、API文档内容和SQL文档内容
     * @return 服务器发送事件发射器
     */
    @PostMapping(value = "/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter generateRDStream(@RequestBody GenerateRequest request) {
        log.info("收到流式生成RD文档请求");
        
        SseEmitter emitter = new SseEmitter(30 * 60 * 1000L); // 30分钟超时
        
        // 设置SSE相关的响应头
        emitter.onCompletion(() -> log.info("SSE连接完成"));
        emitter.onTimeout(() -> log.warn("SSE连接超时"));
        emitter.onError(e -> log.error("SSE连接错误", e));

        // 参数验证
        if (StringUtils.isEmpty(request.getFeishuUrl()) && StringUtils.isEmpty(request.getPrdContent())) {
            String errorMsg = "feishuUrl和prdContent至少需要提供一个";
            log.error(errorMsg);
            sendErrorToClient(emitter, errorMsg);
            return emitter;
        }

        // 获取当前登录用户信息
        UserInfoVo userInfo = userService.getCurrentUserInfo();
        if (userInfo == null) {
            String errorMsg = "用户未登录";
            log.error(errorMsg);
            sendErrorToClient(emitter, errorMsg);
            return emitter;
        }
        final String account = userInfo.getAccount();
        final String department = userInfo.getDepartmentName();
        log.info("当前登录用户: {}, 部门: {}", account, department);

        // 如果提供了飞书URL，先验证并获取文档内容
        String prdContent = request.getPrdContent();
        String originalPrdContent = prdContent;
        if (StringUtils.isNotEmpty(request.getFeishuUrl())) {
            String documentId = parseDocumentId(request.getFeishuUrl());
            if (StringUtils.isEmpty(documentId)) {
                String errorMsg = "无法解析文档ID, docUrl: " + request.getFeishuUrl();
                log.error(errorMsg);
                sendErrorToClient(emitter, errorMsg);
                return emitter;
            }

            try {
                boolean isWiki = request.getFeishuUrl().contains("/wiki/");
                String docContent = feiShuDocumentService.getDocumentContent(documentId, isWiki);
                if (StringUtils.isEmpty(docContent)) {
                    String errorMsg = "获取PRD文档内容失败, documentId: " + documentId;
                    log.error(errorMsg);
                    sendErrorToClient(emitter, errorMsg);
                    return emitter;
                }
                prdContent = docContent;
            } catch (Exception e) {
                String errorMsg = "获取飞书文档内容失败: " + e.getMessage();
                log.error(errorMsg, e);
                sendErrorToClient(emitter, errorMsg);
                return emitter;
            }
        }

        // 异步处理生成任务
        final String finalPrdContent = prdContent;
        executorService.execute(() -> {
            // 创建章节内容映射，用于按序号收集所有生成的内容
            final Map<Integer, String> chapterContents = new TreeMap<>();
            // 提取meegoUrl中的workItemID
            Long meegoWorkItemId = projectWorkItemService.extractWorkItemIdFromUrl(request.getMeegoUrl());
            log.info("meegoWorkItemId: {}", meegoWorkItemId);
            // Mock数据
//            Long meegoWorkItemId = 2546916L;

            try {
                // 1. 术语提取与知识库查询推送
                emitter.send(SseEmitter.event().name("question_extraction").data("{\"status\":\"start\"}"));
                List<KnowledgeExtractorService.KnowledgeQuery> knowledgeQueries = rdGeneratorStreamService.extractKnowledgeQueriesByLLM(finalPrdContent);
                int totalQuestions = 0;
                for (KnowledgeExtractorService.KnowledgeQuery kq : knowledgeQueries) {
                    if (kq.getQuestions() != null) {
                        totalQuestions += kq.getQuestions().size();
                    }
                }
                emitter.send(SseEmitter.event().name("question_extraction").data("{\"status\":\"end\",\"count\":" + totalQuestions + "}"));

                ObjectMapper objectMapper = new ObjectMapper();
                List<String> knowledges = new java.util.ArrayList<>();
                for (KnowledgeExtractorService.KnowledgeQuery kq : knowledgeQueries) {
                    if (kq.getQuestions() != null) {
                        for (String question : kq.getQuestions()) {
                            try {
                                KnowledgeQueryRequest queryRequest = new KnowledgeQueryRequest();
                                queryRequest.setQueryText(question);
                                // 优先用请求参数，否则用默认值
                                String userName = StringUtils.isNotBlank(account) ? account : defaultUserName;
                                Integer knowledgeId = request.getKnowledgeId() != null ? request.getKnowledgeId() : defaultKnowledgeId;
                                queryRequest.setUserName(userName);
                                queryRequest.setKnowledgeId(knowledgeId);
                                queryRequest.setLimit(defaultLimit);
                                log.info("知识库查询请求: {}", queryRequest);
                                List<String> results = knowledgeService.queryKnowledge(queryRequest);
                                knowledges.addAll(results);
                                String eventJson = objectMapper.writeValueAsString(new KnowledgeEvent(question, results));
                                emitter.send(SseEmitter.event().name("knowledge").data(eventJson));
                            } catch (Exception e) {
                                log.warn("知识库查询或推送失败，question:{}", question, e);
                            }
                        }
                    }
                }
                
                // 发送开始事件
                emitter.send(SseEmitter.event()
                    .name("start")
                    .data("开始生成RD文档..."));
                
                // 流式生成RD文档参数
                final String finalApiDocContent = request.getApiDocContent();
                final String finalSqlDocContent = request.getSqlDocContent();
                final String finalFeishuUrl = request.getFeishuUrl();

                //如果需求链接feishuUrl存在，则在finalPrdContent前补充PRD文档链接
                String modifiedPrdContent = finalPrdContent;
                if (StringUtils.isNotEmpty(finalFeishuUrl)) {
                    modifiedPrdContent = "产品需求：(" + finalFeishuUrl + ")\n" + finalPrdContent;
                }
                
                // 生成文档内容，传递knowledges
                rdGeneratorStreamService.generateStream(
                    modifiedPrdContent,
                    finalApiDocContent,
                    finalSqlDocContent,
                    knowledges,
                    chunk -> {
                        try {
                            // 将内容按行分割，每行单独发送
                            String[] lines = chunk.split("\n");
                            for (String line : lines) {
                                // 检查是否是progress事件
                                if (line.startsWith("<event:progress>") && line.endsWith("</event:progress>")) {
                                    // 提取JSON数据
                                    String jsonData = line.substring("<event:progress>".length(), line.length() - "</event:progress>".length());
                                    // 发送progress事件
                                    emitter.send(SseEmitter.event()
                                        .name("progress")
                                        .data(jsonData));
                                } else {
                                    // 发送普通chunk事件
                                    emitter.send(SseEmitter.event()
                                        .name("chunk")
                                        .data(line, MediaType.TEXT_MARKDOWN));
                                }
                            }
                            // 发送一个空行作为分隔符
                            emitter.send(SseEmitter.event()
                                .name("chunk")
                                .data("", MediaType.TEXT_MARKDOWN));
                            
                            // 解析章节编号和内容
                            Integer chapterNumber = null;
                            String chapterContent = null;
                            
                            // 提取章节编号和内容
                            if (chunk.startsWith("<chapter:")) {
                                // 提取章节编号
                                int startPos = "<chapter:".length();
                                int endPos = chunk.indexOf('>', startPos);
                                if (endPos > startPos) {
                                    try {
                                        chapterNumber = Integer.parseInt(chunk.substring(startPos, endPos));
                                        
                                        // 提取章节内容
                                        String endTag = "</chapter:" + chapterNumber + ">";
                                        int contentStart = endPos + 1;
                                        int contentEnd = chunk.lastIndexOf(endTag);
                                        
                                        if (contentEnd > contentStart) {
                                            chapterContent = chunk.substring(contentStart, contentEnd);

                                            // 从章节编号获取章节名称
                                            String sectionName = null;
                                            if (chapterNumber != null && chapterNumber > 0 && chapterNumber <= RDTemplateManager.finalOrderedSections.size()) {
                                                sectionName = RDTemplateManager.finalOrderedSections.get(chapterNumber - 1);
                                            }

                                            // 获取章节配置
                                            SectionConfig sectionConfig = null;
                                            if (StringUtils.isNotBlank(sectionName)) {
                                                sectionConfig = RDTemplateManager.getSectionConfigs().get(sectionName);
                                            }

                                            // 如果convertMermaid为true，处理Mermaid图
                                            if (sectionConfig != null && sectionConfig.isConvertMermaid()) {
                                                log.info("Processing Mermaid for chapter number: {}", chapterNumber);
                                                chapterContent = markdownProcessorService.processMarkdownText(chapterContent);
                                                log.info("Finished processing Mermaid for chapter number: {}", chapterNumber);
                                            }

                                            // 保存章节内容
                                            chapterContents.put(chapterNumber, chapterContent);
                                            log.debug("成功保存章节 {} 的内容", chapterNumber);
                                        }
                                    } catch (NumberFormatException e) {
                                        log.error("解析章节编号失败", e);
                                    }
                                }
                            }
                        } catch (Exception e) {
                            log.error("发送SSE事件失败", e);
                            try {
                                emitter.send(SseEmitter.event()
                                    .name("chunk_error")
                                    .data("处理内容块时发生错误: " + e.getMessage(), MediaType.TEXT_PLAIN));
                            } catch (Exception ex) {
                                log.error("发送chunk_error事件失败", ex);
                            }
                        }
                    });
                
                // 生成完成后，按章节顺序拼接内容并保存到数据库
                try {
                    // 按章节顺序拼接内容
                    StringBuilder resultBuilder = new StringBuilder();
                    // 使用TreeMap自动按序号从小到大排序
                    for (Map.Entry<Integer, String> entry : chapterContents.entrySet()) {
                        resultBuilder.append(entry.getValue()).append("\n\n");
                    }
                    
                    // 添加额外的模板内容
                    resultBuilder.append(rdGeneratorStreamService.getRDTemplateManager().getExtraTemplateContent());
                    
                    String resultContent = resultBuilder.toString();
                    if (StringUtils.isNotBlank(resultContent)) {
                        // 获取PRD文档标题
                        String prdTitle = "";
                        if (StringUtils.isNotEmpty(resultContent)) {
                            // 获取第一行内容（到第一个换行符为止）
                            int firstNewlineIndex = resultContent.indexOf('\n');
                            String firstLine = firstNewlineIndex > 0 ? resultContent.substring(0, firstNewlineIndex) : resultContent;
                            // 提取第一个 # 后面的内容
                            int hashIndex = firstLine.indexOf('#');
                            if (hashIndex >= 0) {
                                prdTitle = firstLine.substring(hashIndex + 1).trim();
                            }
                        }

                        // 格式化当前日期
                        String currentDate = new java.text.SimpleDateFormat("yyyy/MM/dd").format(new java.util.Date());

                        String title = String.format("%s-%s-技术设计文档-%s", 
                            department != null ? department : "小米公司",
                            StringUtils.isNotBlank(prdTitle) ? prdTitle : "产品需求",
                            currentDate);

                        // 1. 将resultContent（markdown）转换为docx
                        // String docxPath = rdGeneratorService.convertMarkdownToDocx(resultContent);
                        // FileTypeEnum fileType = FileTypeEnum.DOCX;
                        String docxPath = rdGeneratorService.uploadMarkdown(resultContent);
                        FileTypeEnum fileType = FileTypeEnum.MD;
                        File docxFile = new File(docxPath);

                        // 2. 上传飞书
                        String accessToken;
                        try {
                            accessToken = feiShuAuthParamsService.getAccessTokenByRedis(account);
                        } catch (Exception e) {
                            accessToken = "";
                            log.error("获取飞书user_accessToken失败", e);
                        } 

                        DocumentEntity documentEntity = new DocumentEntity();
                        documentEntity.setTitle(title);
                        documentEntity.setFileType(fileType);
                        documentEntity.setUserAccessToken(accessToken);
                        documentEntity.setFolderToken(feiShuSpaceService.getRootFolderMeta(accessToken).getData().getToken());
                        DocumentResult documentResult = feiShuUploadFileService.uploadFile(documentEntity, docxFile);
                        String rdDocUrl = documentResult.getUrl();

                        // 只有在用户ID不为空的情况下才保存文档
                        if (StringUtils.isNotBlank(account)) {
                            // 构建知识库内容JSON字符串
                            Map<String, Object> knowledgeMap = new TreeMap<>();
                            knowledgeMap.put("knowledgeSpaceId", request.getKnowledgeSpaceId());
                            knowledgeMap.put("knowledgeId", request.getKnowledgeId());
                            String knowledgeJsonContent = objectMapper.writeValueAsString(knowledgeMap);

                            Long recordId = documentStorageService.saveDocument(
                                account, request.getMeegoUrl(), finalFeishuUrl, rdDocUrl, originalPrdContent,
                                finalApiDocContent, finalSqlDocContent, knowledgeJsonContent, resultContent
                            );
                            log.info("成功保存生成的文档到数据库, id: {}, account: {}", recordId, account);
                        } else {
                            log.warn("无法获取当前登录用户ID，不保存文档生成记录");
                        }

                        try {
                            emitter.send(SseEmitter.event()
                                .name("feishu-link")
                                .data(rdDocUrl, MediaType.TEXT_PLAIN));
                        } catch (Exception sendEx) {
                            log.warn("发送feishu-link事件失败", sendEx);
                        }

                        // 如果meegoUrl有效，则生成飞书文档并回填Meego
                        if (meegoWorkItemId != null) {
                            try {
                                // 3. 回填meego
                                projectWorkItemService.updateRdDocField(meegoWorkItemId, rdDocUrl);
                                log.info("已回填meego表单, workItemId: {}", meegoWorkItemId);                      
                            } catch (Exception e) {
                                log.error("meego表单RD文档回填失败, workItemId: {}", meegoWorkItemId, e);
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("保存文档内容到数据库失败", e);
                    // 不影响正常流程继续
                }
                
                // 发送完成事件
                try {
                    emitter.send(SseEmitter.event()
                        .name("complete")
                        .data("RD文档生成完成"));
                    // 无论是否保存成功，都调用complete方法结束事件流
                    emitter.complete();
                } catch (Exception e) {
                    log.error("发送完成事件失败", e);
                    try {
                        emitter.completeWithError(e);
                    } catch (Exception ex) {
                        log.error("结束事件流失败", ex);
                    }
                }
                
            } catch (Exception e) {
                log.error("生成RD文档失败", e);
                try {
                    sendErrorToClient(emitter, "生成RD文档失败: " + e.getMessage());
                } catch (Exception ex) {
                    log.error("在异步线程中发送错误消息失败", ex);
                }
            }
        });
        
        return emitter;
    }

    // knowledge事件推送结构体
    private static class KnowledgeEvent {
        public String query;
        public List<String> result;
        public KnowledgeEvent(String query, List<String> result) {
            this.query = query;
            this.result = result;
        }
    }
} 
