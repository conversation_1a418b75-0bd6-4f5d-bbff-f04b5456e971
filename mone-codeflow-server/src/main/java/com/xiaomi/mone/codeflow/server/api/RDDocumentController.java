package com.xiaomi.mone.codeflow.server.api;

import com.xiaomi.mone.codeflow.dto.common.ApiResponse;
import com.xiaomi.mone.codeflow.dto.common.PageRequest;
import com.xiaomi.mone.codeflow.dto.common.PageResult;
import com.xiaomi.mone.codeflow.dto.document.RdDocumentRecordDto;
import com.xiaomi.mone.codeflow.dto.document.RdDocumentRecordDetailDto;
import com.xiaomi.mone.codeflow.dto.user.UserInfoVo;
import com.xiaomi.mone.codeflow.service.cas.UserService;
import com.xiaomi.mone.codeflow.service.document.DocumentStorageService;
import com.xiaomi.mone.codeflow.service.markdown.MarkdownProcessorService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Collections;

/**
 * 技术文档管理接口
 */
@Slf4j
@RestController
@RequestMapping("/api/rd-documents")
public class RDDocumentController {

    @Autowired
    private DocumentStorageService documentStorageService;

    @Autowired
    private UserService userService;

    @Autowired
    private MarkdownProcessorService markdownProcessorService;

    /**
     * 获取当前登录用户account
     *
     * @return 用户account，未登录时返回null
     */
    private String getCurrentUserAccount() {
        UserInfoVo userInfo = userService.getCurrentUserInfo();
        if (userInfo != null && StringUtils.isNotBlank(userInfo.getAccount())) {
            return userInfo.getAccount();
        }
        return null;
    }

    /**
     * 获取用户文档生成记录列表
     *
     * @param pageRequest 分页请求参数
     * @return 文档生成记录分页列表
     */
    @PostMapping("/list")
    public ApiResponse<PageResult<RdDocumentRecordDto>> getDocumentList(@RequestBody PageRequest pageRequest) {
        // 获取当前登录用户account
        String uid = getCurrentUserAccount();

        // 如果未登录，返回空结果
        if (StringUtils.isBlank(uid)) {
            log.warn("当前用户未登录，无法获取文档生成记录");
            PageResult<RdDocumentRecordDto> emptyResult = PageResult.of(pageRequest.getPageNum(), pageRequest.getPageSize(), 0, Collections.emptyList());
            return ApiResponse.success(emptyResult);
        }

        log.info("获取用户文档生成记录列表，uid: {}, pageNum: {}, pageSize: {}",
                uid, pageRequest.getPageNum(), pageRequest.getPageSize());

        // 查询文档列表
        PageResult<RdDocumentRecordDto> result = documentStorageService.getDocumentsByUid(uid, pageRequest.getPageNum(), pageRequest.getPageSize());
        return ApiResponse.success(result);
    }

    /**
     * 获取文档生成记录详情
     *
     * @param id 记录ID
     * @return 文档生成记录详情
     */
    @GetMapping("/{id}")
    public ApiResponse<RdDocumentRecordDetailDto> getDocument(@PathVariable("id") Long id) {
        // 获取当前登录用户account
        String uid = getCurrentUserAccount();

        // 如果未登录，返回错误响应
        if (StringUtils.isBlank(uid)) {
            log.warn("当前用户未登录，返回错误响应, id: {}", id);
            return ApiResponse.error("用户未登录");
        }

        log.info("获取文档生成记录详情，id: {}, uid: {}", id, uid);

        // 查询文档详情，内部会进行权限验证
        RdDocumentRecordDetailDto detailDto = documentStorageService.getDocumentDetail(id, uid);

        // 如果文档不存在或无权限访问，返回错误响应
        if (detailDto == null) {
            log.warn("文档不存在或无权限访问，返回错误响应, id: {}, uid: {}", id, uid);
            return ApiResponse.error("文档不存在或无权限访问");
        }

        return ApiResponse.success(detailDto);
    }

    /**
     * 删除文档生成记录
     *
     * @param id 记录ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    public ApiResponse<Void> deleteDocument(@PathVariable("id") Long id) {
        // 获取当前登录用户account
        String uid = getCurrentUserAccount();

        // 如果未登录，返回未授权错误
        if (StringUtils.isBlank(uid)) {
            log.warn("当前用户未登录，无法删除文档, id: {}", id);
            return ApiResponse.errorNoData("用户未登录");
        }

        log.info("删除文档生成记录，id: {}, uid: {}", id, uid);

        // 先查询文档详情，确认是否存在以及是否有权限
        RdDocumentRecordDetailDto detailDto = documentStorageService.getDocumentDetail(id, uid);

        // 如果文档不存在或无权限访问
        if (detailDto == null) {
            log.warn("文档不存在或无权限删除, id: {}, uid: {}", id, uid);
            return ApiResponse.errorNoData("文档不存在或无权限删除");
        }

        // 验证当前用户是否为文档所有者
        if (!StringUtils.equals(uid, detailDto.getUid())) {
            log.warn("当前用户不是文档所有者，无法删除, id: {}, 文档所有者: {}, 当前用户: {}",
                    id, detailDto.getUid(), uid);
            return ApiResponse.errorNoData("只有文档所有者才能删除文档");
        }

        // 执行删除操作
        boolean success = documentStorageService.deleteDocument(id);

        if (success) {
            log.info("文档删除成功, id: {}, uid: {}", id, uid);
            ApiResponse<Void> response = new ApiResponse<>();
            response.setCode(0);
            response.setMessage("success");
            return response;
        } else {
            log.error("文档删除失败, id: {}, uid: {}", id, uid);
            return ApiResponse.errorNoData("文档删除失败");
        }
    }

    /**
     * 转换Markdown文本，处理Mermaid图
     *
     * @param file 上传的Markdown文件
     * @return 处理后的Markdown文本
     */
    @PostMapping("/markdownCovert")
    public ApiResponse<String> markdownCovert(@RequestParam("file") MultipartFile file) {
        log.info("Received markdown file convert request: {}", file.getOriginalFilename());

        if (file.isEmpty()) {
            log.warn("Uploaded file is empty");
            return ApiResponse.error("Uploaded file cannot be empty");
        }

        try {
            String markdownContent = new String(file.getBytes());

            String processedContent = markdownProcessorService.processMarkdownText(markdownContent);
            log.info("Successfully processed markdown content from file: {}", file.getOriginalFilename());
            return ApiResponse.success(processedContent);
        } catch (Exception e) {
            log.error("Failed to process uploaded markdown file: {}", file.getOriginalFilename(), e);
            return ApiResponse.error("Failed to process markdown file");
        }
    }
}