package com.xiaomi.mone.codeflow.server.api;

import com.xiaomi.mone.codeflow.dto.common.ApiResponse;
import com.xiaomi.mone.codeflow.service.llm.McpService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * MCP (Model Context Protocol) 控制器
 * 提供MCP相关的API接口
 */
@Slf4j
@RestController
@RequestMapping("/api/mcp")
public class McpController {

    private final McpService mcpService;

    @Autowired
    public McpController(McpService mcpService) {
        this.mcpService = mcpService;
    }

    /**
     * 问答接口：POST /api/mcp/ask
     */
    @PostMapping("/ask")
    public ApiResponse<String> ask(@RequestBody AskRequest request) {
        if (request == null || request.getQuestion() == null || request.getQuestion().trim().isEmpty()) {
            return ApiResponse.error("问题不能为空");
        }
        try {
            String answer = mcpService.processQuestion(request.getQuestion());
            return ApiResponse.success(answer);
        } catch (Exception e) {
            log.error("MCP问答接口异常", e);
            return ApiResponse.error("服务异常: " + e.getMessage());
        }
    }

    /**
     * 获取MCP配置：GET /api/mcp/config
     */
    @GetMapping("/config")
    public ApiResponse<Map<String, Object>> config() {
        try {
            Map<String, Object> config = mcpService.getMcpConfig();
            return ApiResponse.success(config);
        } catch (Exception e) {
            log.error("获取MCP配置异常", e);
            return ApiResponse.error("服务异常: " + e.getMessage());
        }
    }

    /**
     * 检查MCP服务状态：GET /api/mcp/status
     */
    @GetMapping("/status")
    public ApiResponse<Boolean> status() {
        try {
            boolean status = mcpService.checkMcpStatus();
            return ApiResponse.success(status);
        } catch (Exception e) {
            log.error("检查MCP服务状态异常", e);
            return ApiResponse.error("服务异常: " + e.getMessage());
        }
    }

    /**
     * 问答请求体
     */
    @Data
    public static class AskRequest {
        private String question;
    }

    /**
     * 统一API响应结构
     */
    @Data
    public static class ApiResponse<T> {
        private int code;
        private String msg;
        private T data;

        public static <T> ApiResponse<T> success(T data) {
            ApiResponse<T> resp = new ApiResponse<>();
            resp.setCode(0);
            resp.setMsg("success");
            resp.setData(data);
            return resp;
        }
        public static <T> ApiResponse<T> error(String msg) {
            ApiResponse<T> resp = new ApiResponse<>();
            resp.setCode(1);
            resp.setMsg(msg);
            resp.setData(null);
            return resp;
        }
    }
}
