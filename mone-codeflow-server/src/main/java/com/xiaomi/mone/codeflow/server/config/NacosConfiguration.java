package com.xiaomi.mone.codeflow.server.config;

import com.alibaba.nacos.api.annotation.NacosProperties;
import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.spring.context.annotation.config.EnableNacosConfig;
import com.alibaba.nacos.spring.context.annotation.config.NacosPropertySource;
import com.xiaomi.data.push.nacos.NacosNaming;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Properties;

@Configuration
@EnableNacosConfig(globalProperties = @NacosProperties(serverAddr = "${nacos.config.addrs}"))
@NacosPropertySource(dataId = "mone-codeflow", autoRefreshed = true)
@NacosPropertySource(dataId = "mone-codeflow-feishu" ,autoRefreshed = true)
@NacosPropertySource(dataId = "mone-codeflow-feishu-project" ,autoRefreshed = true)
@NacosPropertySource(dataId = "mone-codeflow-llm" ,autoRefreshed = true)
@NacosPropertySource(dataId = "mone-codeflow-pandoc" ,autoRefreshed = true)
@NacosPropertySource(dataId = "mone-codeflow-zeus" ,autoRefreshed = true)
public class NacosConfiguration {

    @Value("${nacos.config.addrs}")
    private String serverAddr;

    @Value("${dubbo.registry.address}")
    private String nacosAddress;

    @Bean
    public NacosNaming nacosNaming() {
        NacosNaming nacosNaming = new NacosNaming();
        String address = nacosAddress.split("//")[1];
        nacosNaming.setServerAddr(address);
        nacosNaming.init();
        return nacosNaming;
    }

    @Bean
    public ConfigService configService() throws Exception {
        Properties properties = new Properties();
        properties.put("serverAddr", serverAddr);
        // 如有需要可加命名空间等参数
        return NacosFactory.createConfigService(properties);
    }
}